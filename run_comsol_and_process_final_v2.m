function out = run_comsol_and_process_final_v5_final
% =========================================================================
%     COMSOL 计算与后处理整合批处理脚本 (V5 - 类型修正最终版)
%     作者: Gemini
%     日期: 2025年7月3日
%     描述: 此最终版本为对 V4 的最终修正。
%           1. [关键修正] 修复了调用 generate_filename 函数时因参数类型不匹配
%              (table vs struct) 导致的错误。
%           2. 保留已验证成功的仿真内核和硬盘中转后处理方案。
%           这应是可稳定运行的最终版本。
% =========================================================================

import com.comsol.model.*
import com.comsol.model.util.*

% --- ★★★ 使用者配置区域 ★★★ ---
paramsTablePath = 'E:\QYC-storage\dataset_晶体管表面加不同热阻\controlgroup_fixed_ABCD_hL_variable_CuCu.csv';
output_pos_dir = 'E:\QYC-storage\dataset_晶体管表面加不同热阻\position';
output_itr_dir = 'E:\QYC-storage\dataset_晶体管表面加不同热阻\itr';
status_log_dir = 'E:\QYC-storage\dataset_晶体管表面加不同热阻\logs';
statusCsvPath = fullfile(status_log_dir, 'controlgroup_processing_status_final.csv');
% --- 配置结束 ---


% --- 1. 初始化环境 ---
if ~isfolder(output_pos_dir), mkdir(output_pos_dir); end
if ~isfolder(output_itr_dir), mkdir(output_itr_dir); end
if ~isfolder(status_log_dir), mkdir(status_log_dir); end

% --- 2. 加载参数与状态表 ---
if isfile(statusCsvPath)
    fprintf('从 %s 加载现有状态。\n', statusCsvPath);
    paramsTable = readtable(statusCsvPath);
else
    fprintf('未找到状态文件，从 %s 加载原始参数表。\n', paramsTablePath);
    paramsTable = readtable(paramsTablePath);
    paramsTable.status = repmat({''}, height(paramsTable), 1);
    writetable(paramsTable, statusCsvPath);
end
fprintf('总计 %d 行参数，准备顺序处理。\n', height(paramsTable));
total_rows = height(paramsTable);


% --- 3. 主处理循环 (单线程) ---
main_start_time = tic;
for i = 1:total_rows

    currentRow = paramsTable(i, :);

    if strcmp(currentRow.status, 'finished')
        fprintf('跳过已完成的第 %d / %d 行。\n', i, total_rows);
        continue;
    end

    fprintf('\n--- 开始处理第 %d / %d 行 ---\n', i, total_rows);

    try
        ModelUtil.clear();
        model = ModelUtil.create('Model');
        model.label(['Model_Iter_' num2str(i) '.mph']);

        % ==================================================================
        % ★★★★★  嵌入最新的 (18:22版) model.m 核心代码 (保持不变) ★★★★★
        % ==================================================================
        
        % --- 步骤 1: 参数设置 ---
        model.param.set('cupper_A_pos', num2str(currentRow.cupper_A_pos));
        model.param.set('cupper_B_pos', num2str(currentRow.cupper_B_pos));
        model.param.set('cupper_C_pos', num2str(currentRow.cupper_C_pos));
        model.param.set('cupper_D_pos', num2str(currentRow.cupper_D_pos));
        model.param.set('Cu_Cu1', num2str(currentRow.Cu_Cu1));
        model.param.set('Cu_Cu2', num2str(currentRow.Cu_Cu2));
        model.param.set('h_Cu_A', num2str(currentRow.h_Cu_A));
        model.param.set('h_Cu_B', num2str(currentRow.h_Cu_B));
        model.param.set('h_Cu_C', num2str(currentRow.h_Cu_C));
        model.param.set('h_Cu_D', num2str(currentRow.h_Cu_D));
        model.param.set('L_Cu_A', num2str(currentRow.L_Cu_A));
        model.param.set('L_Cu_B', num2str(currentRow.L_Cu_B));
        model.param.set('L_Cu_C', num2str(currentRow.L_Cu_C));
        model.param.set('L_Cu_D', num2str(currentRow.L_Cu_D));
        model.param.set('AMP', '1e9');
        model.param.set('Cu_SiO2', '9E+07');
        model.param.set('SiO2_SiO2', '4.00E+08');
        model.param.set('Si_SiO2', '6e8');
        model.param.set('Cu_Si', '2.58e8');
        model.param.set('Si_Si', '2e8');

        % --- 步骤 2: 几何与组件创建 ---
        comp1 = model.component.create('comp1', true);
        geom1 = comp1.geom.create('geom1', 2);
        comp1.mesh.create('mesh1');
        geom1.lengthUnit([native2unicode(hex2dec({'00' 'b5'}), 'unicode') 'm']);
        geom1.create('r1', 'Rectangle').set('size', [5 1]).set('base', 'center').set('pos', [0 0.5]);
        geom1.create('r2', 'Rectangle').set('size', [5 1]).set('base', 'center').set('pos', [0 -0.5]);
        geom1.create('r6', 'Rectangle').set('size', [5 1]).set('base', 'center').set('pos', [0 -1.5]);
        geom1.create('r3', 'Rectangle').set('size', [5 1]).set('base', 'center').set('pos', [0 -2.5]);
        geom1.create('r4', 'Rectangle').set('size', {'L_Cu_A' 'h_Cu_A'}).set('base', 'center').set('pos', {'cupper_A_pos' '-1+h_Cu_A/2'});
        geom1.create('r5', 'Rectangle').set('size', {'L_Cu_B' 'h_Cu_B'}).set('base', 'center').set('pos', {'cupper_B_pos' '-1+h_Cu_B/2'});
        geom1.create('r7', 'Rectangle').set('size', {'L_Cu_C' 'h_Cu_C'}).set('base', 'center').set('pos', {'cupper_C_pos' '-1-h_Cu_C/2'});
        geom1.create('r8', 'Rectangle').set('size', {'L_Cu_D' 'h_Cu_D'}).set('base', 'center').set('pos', {'cupper_D_pos' '-1-h_Cu_D/2'});
        geom1.create('r9', 'Rectangle').set('size', [1 0.2]).set('pos', [-2 1]);
        geom1.create('r10', 'Rectangle').set('size', [1 0.2]).set('pos', [1 1]);
        geom1.create('r11', 'Rectangle').set('size', [0.6 0.2]).set('pos', [-0.3 1]);
        geom1.run;
        geom1.run('fin');

        % --- 步骤 3 - 7 (材料,物理场,网格,属性) 保持不变 ---
        model.component('comp1').material.create('mat1', 'Common');
        model.component('comp1').material.create('mat2', 'Common');
        model.component('comp1').material.create('mat3', 'Common');
        model.component('comp1').material('mat1').selection.set([1 3 4 5 6 8 9 11]);
        model.component('comp1').material('mat2').selection.set([6 7 10 11]);
        model.component('comp1').material('mat3').selection.set([2 3]);
        model.component('comp1').material('mat1').propertyGroup.create('DispersionModelSellmeierModified2', 'DispersionModelSellmeierModified2', 'Sellmeier 2');
        model.component('comp1').material('mat1').propertyGroup.create('RefractiveIndex', 'RefractiveIndex', 'Refractive index');
        model.component('comp1').material('mat2').propertyGroup.create('DispersionModelSellmeierModified2', 'DispersionModelSellmeierModified2', 'Sellmeier 2');
        model.component('comp1').material('mat2').propertyGroup.create('RefractiveIndex', 'RefractiveIndex', 'Refractive index');
        model.component('comp1').material('mat3').propertyGroup.create('DispersionModelSellmeierModified2', 'DispersionModelSellmeierModified2', 'Sellmeier 2');
        model.component('comp1').material('mat3').propertyGroup.create('RefractiveIndex', 'RefractiveIndex', 'Refractive index');
        ht = model.component('comp1').physics.create('ht', 'HeatTransfer', 'geom1');
        ht.create('bhs1', 'BoundaryHeatSource', 1).selection.set([9 10 12 23 24 25 27 28 29 30 32 43 44]);
        ht.create('temp1', 'TemperatureBoundary', 1).selection.set([2]);
        ht.create('tc1', 'ThermalContact', 1).selection.set([4 8]);
        ht.create('tc2', 'ThermalContact', 1).selection.set([18]);
        ht.create('tc3', 'ThermalContact', 1).selection.set([13 14 15 16 17 19 20 21 33 34 35 36 38 39 40 41]);
        ht.create('tc4', 'ThermalContact', 1).selection.set([6 22 42]);
        ht.create('tc5', 'ThermalContact', 1).selection.set([37]);
        model.component('comp1').mesh('mesh1').autoMeshSize(2);
        model.component('comp1').material('mat1').label('Si');
        model.component('comp1').material('mat1').propertyGroup('def').set('thermalconductivity', {'149' '0' '0' '0' '149' '0' '0' '0' '149'});
        model.component('comp1').material('mat1').propertyGroup('def').set('density', '2330');
        model.component('comp1').material('mat1').propertyGroup('def').set('heatcapacity', '705');
        model.component('comp1').material('mat1').propertyGroup('DispersionModelSellmeierModified2').set('ODsmc', {'1.28604141' '1.07044083' '1.10202242' '1.00585997e-2' '100'});
        model.component('comp1').material('mat1').propertyGroup('DispersionModelSellmeierModified2').set('Trefsmc', '22[degC]');
        model.component('comp1').material('mat1').propertyGroup('DispersionModelSellmeierModified2').set('Prefsmc', '0');
        model.component('comp1').material('mat1').propertyGroup('RefractiveIndex').addInput('frequency');
        model.component('comp1').material('mat2').label('Cu');
        model.component('comp1').material('mat2').propertyGroup('def').set('thermalconductivity', {'401' '0' '0' '0' '401' '0' '0' '0' '401'});
        model.component('comp1').material('mat2').propertyGroup('def').set('density', '8960');
        model.component('comp1').material('mat2').propertyGroup('def').set('heatcapacity', '385');
        model.component('comp1').material('mat2').propertyGroup('DispersionModelSellmeierModified2').set('ODsmc', {'1.28604141' '1.07044083' '1.10202242' '1.00585997e-2' '100'});
        model.component('comp1').material('mat2').propertyGroup('DispersionModelSellmeierModified2').set('Trefsmc', '22[degC]');
        model.component('comp1').material('mat2').propertyGroup('DispersionModelSellmeierModified2').set('Prefsmc', '0');
        model.component('comp1').material('mat2').propertyGroup('RefractiveIndex').addInput('frequency');
        model.component('comp1').material('mat3').label('SiO2');
        model.component('comp1').material('mat3').propertyGroup('def').set('thermalconductivity', {'1.4' '0' '0' '0' '1.4' '0' '0' '0' '1.4'});
        model.component('comp1').material('mat3').propertyGroup('def').set('density', '2200');
        model.component('comp1').material('mat3').propertyGroup('def').set('heatcapacity', '740');
        model.component('comp1').material('mat3').propertyGroup('DispersionModelSellmeierModified2').set('ODsmc', {'1.28604141' '1.07044083' '1.10202242' '1.00585997e-2' '100'});
        model.component('comp1').material('mat3').propertyGroup('DispersionModelSellmeierModified2').set('Trefsmc', '22[degC]');
        model.component('comp1').material('mat3').propertyGroup('DispersionModelSellmeierModified2').set('Prefsmc', '0');
        model.component('comp1').material('mat3').propertyGroup('RefractiveIndex').addInput('frequency');
        ht.prop('PhysicalModelProperty').set('dz', '5e-6');
        ht.feature('bhs1').set('Qb_input', 'AMP');
        ht.feature('tc1').set('hgap', 'Si_SiO2');
        ht.feature('tc2').set('hgap', 'Cu_Cu1');
        ht.feature('tc3').set('hgap', 'Cu_SiO2');
        ht.feature('tc4').set('hgap', 'SiO2_SiO2');
        ht.feature('tc5').set('hgap', 'Cu_Cu2');
        
        % --- 步骤 8: 完整的研究和求解器设置 ---
        model.study.create('std1');
        model.study('std1').create('time', 'Transient');
        tlist_str = '10^{range(log10(1.0e-8),1/20,log10(0.001))}';
        model.study('std1').feature('time').set('tlist', tlist_str);
        model.sol.create('sol1');
        model.sol('sol1').attach('std1');
        model.sol('sol1').create('st1', 'StudyStep');
        model.sol('sol1').create('v1', 'Variables');
        model.sol('sol1').create('t1', 'Time');
        model.sol('sol1').feature('t1').create('fc1', 'FullyCoupled');
        model.sol('sol1').feature('t1').create('d1', 'Direct');
        model.sol('sol1').feature('t1').create('i1', 'Iterative');
        model.sol('sol1').feature('t1').feature('i1').create('mg1', 'Multigrid');
        model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('pr').create('so1', 'SOR');
        model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('po').create('so1', 'SOR');
        model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('cs').create('d1', 'Direct');
        model.sol('sol1').feature('t1').feature.remove('fcDef');
        model.sol('sol1').feature('t1').set('tlist', tlist_str);
        model.sol('sol1').feature('t1').set('tstepsbdf', 'strict');
        model.sol('sol1').feature('t1').set('maxorder', 2);
        model.sol('sol1').feature('t1').set('estrat', 'exclude');
        model.sol('sol1').feature('t1').feature('fc1').set('linsolver', 'd1');
        model.sol('sol1').feature('t1').feature('fc1').set('jtech', 'once');
        model.sol('sol1').feature('t1').feature('fc1').set('stabacc', 'aacc');
        model.sol('sol1').feature('t1').feature('fc1').set('aaccdim', 5);
        model.sol('sol1').feature('t1').feature('fc1').set('aaccmix', 0.9);
        model.sol('sol1').feature('t1').feature('fc1').set('aaccdelay', 1);
        model.sol('sol1').feature('t1').feature('fc1').set('damp', '0.9');
        model.sol('sol1').feature('t1').feature('d1').set('linsolver', 'pardiso');
        model.sol('sol1').feature('t1').feature('d1').set('pivotperturb', 1.0E-13);
        model.sol('sol1').feature('t1').feature('i1').feature('mg1').set('prefun', 'saamg');
        model.sol('sol1').feature('t1').feature('i1').feature('mg1').set('maxcoarsedof', 50000);
        model.sol('sol1').feature('t1').feature('i1').feature('mg1').set('saamgcompwise', true);
        model.sol('sol1').feature('t1').feature('i1').feature('mg1').set('usesmooth', false);
        model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('pr').feature('so1').set('relax', 0.9);
        model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('po').feature('so1').set('relax', 0.9);
        model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('cs').feature('d1').set('linsolver', 'pardiso');
        model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('cs').feature('d1').set('pivotperturb', 1.0E-13);

        % --- 运行求解 ---
        fprintf('开始求解第 %d 行的模型...\n', i);
        model.sol('sol1').runAll;
        fprintf('求解成功。\n');

    catch ME_solve
        fprintf('第 %d 行求解失败: %s (在文件 %s, 第 %d 行)\n', i, ME_solve.message, ME_solve.stack(1).name, ME_solve.stack(1).line);
        if ~isempty(ME_solve.cause) && isa(ME_solve.cause{1}, 'matlab.exception.JavaException')
            fprintf('--- Java Exception Stack Trace ---\n');
            stackTrace = ME_solve.cause{1}.ExceptionObject.getStackTrace();
            for k = 1:numel(stackTrace)
                fprintf('at %s\n', char(stackTrace(k).toString()));
            end
            fprintf('--- End Stack Trace ---\n');
        end
        paramsTable.status{i} = 'solve_error';
        writetable(paramsTable, statusCsvPath);
        continue;
    end

    % --- 后处理 ---
    temp_csv_path = fullfile(status_log_dir, ['temp_data_' char(java.util.UUID.randomUUID) '.csv']);
    
    try
        fprintf('导出临时数据文件到硬盘...\n');
        export_node = model.result.export.create('data1', 'Data');
        export_node.set('expr', {'T'});
        export_node.set('filename', temp_csv_path);
        export_node.run();
        fprintf('临时文件已创建: %s\n', temp_csv_path);

        fprintf('从临时文件读取数据...\n');
        all_data_in_memory = readmatrix(temp_csv_path, 'HeaderLines', 9);
        
        fprintf('开始内存后处理...\n');
        
        % ★★★★★★★★★★★★★★★★★★★★ 关键错误修正 ★★★★★★★★★★★★★★★★★★★★
        % 将单行 table 转换为 struct 后再传递给函数
        base_name = generate_filename(table2struct(currentRow));
        % ★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★

        x_col_idx = 1; 
        y_col_idx = 2;
        
        filter_condition = false(size(all_data_in_memory, 1), 1);
        y_is_1_idx = (all_data_in_memory(:, y_col_idx) == 1);
        if any(y_is_1_idx)
            x = all_data_in_memory(y_is_1_idx, x_col_idx);
            cond = (x >= -2.5 & x <= -2) | (x >= -1 & x <= -0.3) | (x >= 0.3 & x <= 1) | (x >= 2 & x <= 2.5);
            filter_condition(y_is_1_idx) = cond;
        end
        y_is_1_2_idx = (all_data_in_memory(:, y_col_idx) == 1.2);
        if any(y_is_1_2_idx)
            x = all_data_in_memory(y_is_1_2_idx, x_col_idx);
            cond = (x >= -2 & x <= -1) | (x >= -0.3 & x <= 0.3) | (x >= 1 & x <= 2);
            filter_condition(y_is_1_2_idx) = filter_condition(y_is_1_2_idx) | cond;
        end
        filtered_data = all_data_in_memory(filter_condition, :);
        clear all_data_in_memory;

        if isempty(filtered_data)
            warning('第 %d 行根据过滤条件未找到任何数据点。', i);
            paramsTable.status{i} = 'processing_error_no_data';
            writetable(paramsTable, statusCsvPath);
            delete(temp_csv_path);
            continue;
        end

        [~, sort_indices] = sort(filtered_data(:, x_col_idx));
        sorted_data = filtered_data(sort_indices,:);
        sorted_last_temps = sorted_data(:, end);
        
        min_T = min(sorted_last_temps); max_T = max(sorted_last_temps);
        if max_T == min_T, norm_T = zeros(size(sorted_last_temps)); else, norm_T = (sorted_last_temps - min_T) / (max_T - min_T); end
        writematrix(norm_T, fullfile(output_pos_dir, [base_name, '_position.csv']));
        fprintf('已保存 _position.csv 文件。\n');

        X1 = currentRow.cupper_A_pos + currentRow.cupper_C_pos / 2;
        X2 = (currentRow.cupper_B_pos + currentRow.cupper_D_pos) / 2;
        
        sorted_x_coords = sorted_data(:, 1);
        idx1 = find(sorted_x_coords < X1, 1, 'last');
        idx2 = find(sorted_x_coords > X1, 1, 'first');
        idx3 = find(sorted_x_coords < X2, 1, 'last');
        idx4 = find(sorted_x_coords > X2, 1, 'first');

        if all([~isempty(idx1), ~isempty(idx2), ~isempty(idx3), ~isempty(idx4)])
            found_indices = unique([idx1; idx2; idx3; idx4]);
            itr_data = sorted_data(found_indices, 3:end);
            writematrix(itr_data, fullfile(output_itr_dir, [base_name, '_itr.csv']));
            fprintf('已保存 _itr.csv 文件。\n');
        else
            warning('第 %d 行未找到 ITR 所需的4个点。', i);
        end
        clear filtered_data sorted_data;
        
        delete(temp_csv_path);
        fprintf('已删除临时文件。\n');

    catch ME_process
        fprintf('第 %d 行后处理失败: %s (在文件 %s, 第 %d 行)\n', i, ME_process.message, ME_process.stack(1).name, ME_process.stack(1).line);
        paramsTable.status{i} = 'processing_error';
        writetable(paramsTable, statusCsvPath);
        
        if exist(temp_csv_path, 'file')
            delete(temp_csv_path);
            fprintf('已删除出错的临时文件。\n');
        end
        continue;
    end

    fprintf('已完成第 %d / %d 行的处理。\n', i, total_rows);
    paramsTable.status{i} = 'finished';
    writetable(paramsTable, statusCsvPath);

end

total_elapsed_time = toc(main_start_time);
fprintf('\n所有任务已处理完毕！总耗时: %.2f 秒 (%.2f 分钟)。\n', total_elapsed_time, total_elapsed_time/60);
out = [];

end

function fname = generate_filename(currentRow)
    % currentRow 现在是一个 struct
    X1 = (currentRow.cupper_A_pos + currentRow.cupper_C_pos) / 2;
    Y1 = 0.5 - abs(currentRow.cupper_A_pos - currentRow.cupper_C_pos);
    X2 = (currentRow.cupper_B_pos + currentRow.cupper_D_pos) / 2;
    Y2 = 0.5 - abs(currentRow.cupper_B_pos - currentRow.cupper_D_pos);
    X1_str = strrep(strrep(num2str(X1,'%.4f'),'.','p'),'-','n');
    Y1_str = strrep(strrep(num2str(Y1,'%.4f'),'.','p'),'-','n');
    X2_str = strrep(strrep(num2str(X2,'%.4f'),'.','p'),'-','n');
    Y2_str = strrep(strrep(num2str(Y2,'%.4f'),'.','p'),'-','n');
    cu1_str = strrep(strrep(num2str(currentRow.Cu_Cu1,'%g'),'.','p'),'+','');
    cu2_str = strrep(strrep(num2str(currentRow.Cu_Cu2,'%g'),'.','p'),'+','');
    hA_str = strrep(num2str(currentRow.h_Cu_A,'%.2f'),'.','p');
    hB_str = strrep(num2str(currentRow.h_Cu_B,'%.2f'),'.','p');
    hC_str = strrep(num2str(currentRow.h_Cu_C,'%.2f'),'.','p');
    hD_str = strrep(num2str(currentRow.h_Cu_D,'%.2f'),'.','p');
    LA_str = strrep(num2str(currentRow.L_Cu_A,'%.2f'),'.','p');
    LB_str = strrep(num2str(currentRow.L_Cu_B,'%.2f'),'.','p');
    LC_str = strrep(num2str(currentRow.L_Cu_C,'%.2f'),'.','p');
    LD_str = strrep(num2str(currentRow.L_Cu_D,'%.2f'),'.','p');
    fname = sprintf('XY1_%s_%s_XY2_%s_%s_C1-%s_C2-%s_h-%s-%s-%s-%s_L-%s-%s-%s-%s', ...
        X1_str, Y1_str, X2_str, Y2_str, ...
        cu1_str, cu2_str, ...
        hA_str, hB_str, hC_str, hD_str, ...
        LA_str, LB_str, LC_str, LD_str);
end